version: '3.8'

networks:
  work-order-network:
    driver: bridge

services:
  # 定时任务服务 - 优先启动
  quartz:
    build:
      context: ./bto-cloud-module/bto-module-quartz
      dockerfile: Dockerfile
    container_name: bto-module-quartz
    ports:
      - "58082:58082"
    environment:
      - JAVA_OPTS=-Xmx1024m -Xms512m -Dspring.profiles.active=dev
      - nacos_host=${NACOS_HOST:-127.0.0.1}
      - nacos_port=${NACOS_PORT:-8848}
      - nacos_namespace=${NACOS_NAMESPACE:-}
    networks:
      - work-order-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:58082/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 消息服务
  message:
    build:
      context: ./bto-cloud-module/bto-module-message
      dockerfile: Dockerfile
    container_name: bto-module-message
    ports:
      - "58083:58083"
    environment:
      - J<PERSON>A_OPTS=-Xmx1024m -Xms512m -Dspring.profiles.active=dev
      - nacos_host=${NACOS_HOST:-127.0.0.1}
      - nacos_port=${NACOS_PORT:-8848}
      - nacos_namespace=${NACOS_NAMESPACE:-}
    networks:
      - work-order-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:58083/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 监控服务
  monitor:
    build:
      context: ./bto-cloud-module/bto-module-monitor
      dockerfile: Dockerfile
    container_name: bto-module-monitor
    ports:
      - "58084:58084"
    environment:
      - JAVA_OPTS=-Xmx512m -Xms256m -Dspring.profiles.active=dev
      - nacos_host=${NACOS_HOST:-127.0.0.1}
      - nacos_port=${NACOS_PORT:-8848}
      - nacos_namespace=${NACOS_NAMESPACE:-}
    networks:
      - work-order-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:58084/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 系统服务
  system:
    build:
      context: ./bto-cloud-system
      dockerfile: Dockerfile
    container_name: bto-cloud-system
    ports:
      - "58081:58081"
    environment:
      - JAVA_OPTS=-Xmx2048m -Xms512m -Dspring.profiles.active=dev
      - nacos_host=${NACOS_HOST:-127.0.0.1}
      - nacos_port=${NACOS_PORT:-8848}
      - nacos_namespace=${NACOS_NAMESPACE:-}
    networks:
      - work-order-network
    depends_on:
      quartz:
        condition: service_healthy
      message:
        condition: service_healthy
      monitor:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:58081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # 工单服务
  workorder:
    build:
      context: ./bto-cloud-workorder
      dockerfile: Dockerfile
    container_name: bto-cloud-workorder
    ports:
      - "58085:58085"
    environment:
      - JAVA_OPTS=-Xmx2048m -Xms512m -Dspring.profiles.active=dev
      - nacos_host=${NACOS_HOST:-127.0.0.1}
      - nacos_port=${NACOS_PORT:-8848}
      - nacos_namespace=${NACOS_NAMESPACE:-}
    networks:
      - work-order-network
    depends_on:
      system:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:58085/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # 网关服务 - 最后启动，作为入口服务
  gateway:
    build:
      context: ./bto-cloud-gateway
      dockerfile: Dockerfile
    container_name: bto-cloud-gateway
    ports:
      - "58080:58080"
    environment:
      - JAVA_OPTS=-Xmx1024m -Xms256m -Dspring.profiles.active=dev
      - nacos_host=${NACOS_HOST:-127.0.0.1}
      - nacos_port=${NACOS_PORT:-8848}
      - nacos_namespace=${NACOS_NAMESPACE:-}
    networks:
      - work-order-network
    depends_on:
      system:
        condition: service_healthy
      workorder:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:58080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s