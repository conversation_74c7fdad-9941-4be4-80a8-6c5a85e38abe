# 博通工单管理系统 Docker Compose 环境变量配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# ===========================================
# Nacos 配置中心连接信息
# ===========================================
# Nacos服务器地址（不包含协议）
NACOS_HOST=127.0.0.1
# Nacos服务器端口
NACOS_PORT=8848
# Nacos命名空间（留空使用默认public命名空间）
NACOS_NAMESPACE=

# ===========================================
# 应用环境配置
# ===========================================
# Spring Boot 环境配置 (dev/test/prod)
SPRING_PROFILES_ACTIVE=dev

# ===========================================
# 网络配置
# ===========================================
# Docker网络名称
COMPOSE_PROJECT_NAME=work-order-cloud

# ===========================================
# 服务端口映射（可选，默认使用docker-compose.yml中的配置）
# ===========================================
# 网关服务端口
GATEWAY_PORT=58080
# 系统服务端口
SYSTEM_PORT=58081
# 定时任务服务端口
QUARTZ_PORT=58082
# 消息服务端口
MESSAGE_PORT=58083
# 监控服务端口
MONITOR_PORT=58084
# 工单服务端口
WORKORDER_PORT=58085

# ===========================================
# JVM 内存配置（可选，默认使用docker-compose.yml中的配置）
# ===========================================
# 网关服务JVM参数
GATEWAY_JAVA_OPTS=-Xmx1024m -Xms256m -Dspring.profiles.active=dev
# 系统服务JVM参数
SYSTEM_JAVA_OPTS=-Xmx2048m -Xms512m -Dspring.profiles.active=dev
# 工单服务JVM参数
WORKORDER_JAVA_OPTS=-Xmx2048m -Xms512m -Dspring.profiles.active=dev
# 定时任务服务JVM参数
QUARTZ_JAVA_OPTS=-Xmx1024m -Xms512m -Dspring.profiles.active=dev
# 消息服务JVM参数
MESSAGE_JAVA_OPTS=-Xmx1024m -Xms512m -Dspring.profiles.active=dev
# 监控服务JVM参数
MONITOR_JAVA_OPTS=-Xmx512m -Xms256m -Dspring.profiles.active=dev
