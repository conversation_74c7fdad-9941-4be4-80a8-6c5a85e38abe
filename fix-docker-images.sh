#!/bin/bash

# Docker镜像问题修复脚本 (Linux/Mac版本)
# 用于修复Docker镜像拉取问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Dockerfile列表
DOCKERFILES=(
    "bto-cloud-gateway/Dockerfile"
    "bto-cloud-system/Dockerfile"
    "bto-cloud-workorder/Dockerfile"
    "bto-cloud-module/bto-module-quartz/Dockerfile"
    "bto-cloud-module/bto-module-message/Dockerfile"
    "bto-cloud-module/bto-module-monitor/Dockerfile"
)

# 更新Dockerfile基础镜像
update_dockerfiles() {
    local old_image=$1
    local new_image=$2
    
    log_info "正在更新Dockerfile..."
    
    for dockerfile in "${DOCKERFILES[@]}"; do
        if [ -f "$dockerfile" ]; then
            sed -i "s|FROM $old_image|FROM $new_image|g" "$dockerfile"
            log_success "已更新 $dockerfile"
        fi
    done
}

# 检查Docker是否运行
check_docker() {
    log_info "检查Docker状态..."
    if ! docker version > /dev/null 2>&1; then
        log_error "Docker未运行或未安装"
        exit 1
    fi
    log_success "Docker运行正常"
}

# 尝试拉取镜像
try_pull_image() {
    local image=$1
    log_info "尝试拉取镜像: $image"
    
    if docker pull "$image" > /dev/null 2>&1; then
        log_success "镜像拉取成功: $image"
        return 0
    else
        log_error "镜像拉取失败: $image"
        return 1
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "  博通工单管理系统 - Docker镜像修复脚本"
    echo "=========================================="
    echo ""
    
    check_docker
    
    # 方案1: 尝试eclipse-temurin镜像
    log_info "方案1: 尝试拉取eclipse-temurin:8-jdk-alpine镜像..."
    if try_pull_image "eclipse-temurin:8-jdk-alpine"; then
        log_success "可以直接使用 docker-compose up -d --build"
        exit 0
    fi
    
    # 方案2: 尝试Amazon Corretto镜像
    log_info "方案2: 尝试拉取amazoncorretto:8-alpine-jdk镜像..."
    if try_pull_image "amazoncorretto:8-alpine-jdk"; then
        update_dockerfiles "eclipse-temurin:8-jdk-alpine" "amazoncorretto:8-alpine-jdk"
        log_success "现在可以使用 docker-compose up -d --build"
        exit 0
    fi
    
    # 方案3: 尝试阿里云镜像
    log_info "方案3: 尝试拉取阿里云镜像..."
    if try_pull_image "registry.cn-hangzhou.aliyuncs.com/acs/openjdk:8-jdk-alpine"; then
        update_dockerfiles "eclipse-temurin:8-jdk-alpine" "registry.cn-hangzhou.aliyuncs.com/acs/openjdk:8-jdk-alpine"
        log_success "现在可以使用 docker-compose up -d --build"
        exit 0
    fi
    
    # 方案4: 尝试腾讯云镜像
    log_info "方案4: 尝试拉取腾讯云镜像..."
    if try_pull_image "ccr.ccs.tencentyun.com/library/openjdk:8-jdk-alpine"; then
        update_dockerfiles "eclipse-temurin:8-jdk-alpine" "ccr.ccs.tencentyun.com/library/openjdk:8-jdk-alpine"
        log_success "现在可以使用 docker-compose up -d --build"
        exit 0
    fi
    
    # 所有方案都失败
    log_error "所有镜像拉取方案都失败"
    echo ""
    log_warning "建议解决方案:"
    echo "1. 检查网络连接"
    echo "2. 配置Docker镜像加速器:"
    echo "   sudo mkdir -p /etc/docker"
    echo "   sudo tee /etc/docker/daemon.json <<-'EOF'"
    echo "   {"
    echo '     "registry-mirrors": ['
    echo '       "https://docker.mirrors.ustc.edu.cn",'
    echo '       "https://hub-mirror.c.163.com",'
    echo '       "https://mirror.baidubce.com"'
    echo '     ]'
    echo "   }"
    echo "   EOF"
    echo "   sudo systemctl daemon-reload"
    echo "   sudo systemctl restart docker"
    echo "3. 重新运行此脚本"
    
    exit 1
}

# 执行主函数
main "$@"
