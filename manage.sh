#!/bin/bash

# 博通工单管理系统 Docker 服务管理脚本
# Author: Augment Agent

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务列表
SERVICES=("gateway" "system" "workorder" "quartz" "message" "monitor")

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "博通工单管理系统 Docker 服务管理脚本"
    echo ""
    echo "用法: $0 [命令] [服务名]"
    echo ""
    echo "命令:"
    echo "  start [service]     启动服务（不指定服务名则启动所有服务）"
    echo "  stop [service]      停止服务（不指定服务名则停止所有服务）"
    echo "  restart [service]   重启服务（不指定服务名则重启所有服务）"
    echo "  status              查看服务状态"
    echo "  logs [service]      查看服务日志（不指定服务名则查看所有日志）"
    echo "  health              检查服务健康状态"
    echo "  clean               清理停止的容器和未使用的镜像"
    echo "  rebuild [service]   重新构建并启动服务"
    echo ""
    echo "服务名:"
    echo "  gateway    - 网关服务 (端口: 58080)"
    echo "  system     - 系统服务 (端口: 58081)"
    echo "  workorder  - 工单服务 (端口: 58085)"
    echo "  quartz     - 定时任务服务 (端口: 58082)"
    echo "  message    - 消息服务 (端口: 58083)"
    echo "  monitor    - 监控服务 (端口: 58084)"
    echo ""
    echo "示例:"
    echo "  $0 start              # 启动所有服务"
    echo "  $0 start gateway      # 启动网关服务"
    echo "  $0 logs system        # 查看系统服务日志"
    echo "  $0 restart workorder  # 重启工单服务"
}

# 检查服务名是否有效
validate_service() {
    local service=$1
    if [[ " ${SERVICES[@]} " =~ " ${service} " ]]; then
        return 0
    else
        log_error "无效的服务名: $service"
        echo "有效的服务名: ${SERVICES[*]}"
        return 1
    fi
}

# 启动服务
start_services() {
    local service=$1
    if [ -n "$service" ]; then
        validate_service "$service" || return 1
        log_info "启动服务: $service"
        docker-compose up -d "$service"
    else
        log_info "启动所有服务"
        docker-compose up -d
    fi
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    local service=$1
    if [ -n "$service" ]; then
        validate_service "$service" || return 1
        log_info "停止服务: $service"
        docker-compose stop "$service"
    else
        log_info "停止所有服务"
        docker-compose down
    fi
    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    local service=$1
    if [ -n "$service" ]; then
        validate_service "$service" || return 1
        log_info "重启服务: $service"
        docker-compose restart "$service"
    else
        log_info "重启所有服务"
        docker-compose restart
    fi
    log_success "服务重启完成"
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
}

# 查看服务日志
show_logs() {
    local service=$1
    if [ -n "$service" ]; then
        validate_service "$service" || return 1
        log_info "查看服务日志: $service"
        docker-compose logs -f "$service"
    else
        log_info "查看所有服务日志"
        docker-compose logs -f
    fi
}

# 检查服务健康状态
check_health() {
    log_info "检查服务健康状态..."
    
    declare -A service_ports=(
        ["gateway"]=58080
        ["system"]=58081
        ["workorder"]=58085
        ["quartz"]=58082
        ["message"]=58083
        ["monitor"]=58084
    )
    
    for service in "${SERVICES[@]}"; do
        port=${service_ports[$service]}
        if curl -f -s http://localhost:$port/actuator/health > /dev/null 2>&1; then
            log_success "$service (端口: $port) - 健康"
        else
            log_error "$service (端口: $port) - 不健康或未启动"
        fi
    done
}

# 清理资源
clean_resources() {
    log_info "清理停止的容器和未使用的镜像..."
    docker container prune -f
    docker image prune -f
    log_success "清理完成"
}

# 重新构建服务
rebuild_service() {
    local service=$1
    if [ -n "$service" ]; then
        validate_service "$service" || return 1
        log_info "重新构建服务: $service"
        docker-compose stop "$service"
        docker-compose build --no-cache "$service"
        docker-compose up -d "$service"
    else
        log_info "重新构建所有服务"
        docker-compose down
        docker-compose build --no-cache
        docker-compose up -d
    fi
    log_success "服务重新构建完成"
}

# 主函数
main() {
    local command=$1
    local service=$2
    
    case $command in
        "start")
            start_services "$service"
            ;;
        "stop")
            stop_services "$service"
            ;;
        "restart")
            restart_services "$service"
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$service"
            ;;
        "health")
            check_health
            ;;
        "clean")
            clean_resources
            ;;
        "rebuild")
            rebuild_service "$service"
            ;;
        "help"|"-h"|"--help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
