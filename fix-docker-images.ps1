# Docker镜像问题修复脚本 (PowerShell版本)
# 用于Windows环境下修复Docker镜像拉取问题

Write-Host "博通工单管理系统 - Docker镜像修复脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 检查Docker是否运行
Write-Host "检查Docker状态..." -ForegroundColor Blue
try {
    docker version | Out-Null
    Write-Host "✅ Docker运行正常" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker未运行或未安装" -ForegroundColor Red
    exit 1
}

# 方案1: 尝试拉取eclipse-temurin镜像
Write-Host "`n方案1: 尝试拉取eclipse-temurin:8-jdk-alpine镜像..." -ForegroundColor Blue
try {
    docker pull eclipse-temurin:8-jdk-alpine
    Write-Host "✅ eclipse-temurin镜像拉取成功" -ForegroundColor Green
    Write-Host "可以直接使用 docker-compose up -d --build" -ForegroundColor Green
    exit 0
} catch {
    Write-Host "❌ eclipse-temurin镜像拉取失败" -ForegroundColor Red
}

# 方案2: 尝试拉取Amazon Corretto镜像
Write-Host "`n方案2: 尝试拉取amazoncorretto:8-alpine-jdk镜像..." -ForegroundColor Blue
try {
    docker pull amazoncorretto:8-alpine-jdk
    Write-Host "✅ Amazon Corretto镜像拉取成功" -ForegroundColor Green
    
    # 替换所有Dockerfile中的基础镜像
    Write-Host "正在更新Dockerfile..." -ForegroundColor Blue
    
    $dockerfiles = @(
        "bto-cloud-gateway/Dockerfile",
        "bto-cloud-system/Dockerfile", 
        "bto-cloud-workorder/Dockerfile",
        "bto-cloud-module/bto-module-quartz/Dockerfile",
        "bto-cloud-module/bto-module-message/Dockerfile",
        "bto-cloud-module/bto-module-monitor/Dockerfile"
    )
    
    foreach ($dockerfile in $dockerfiles) {
        if (Test-Path $dockerfile) {
            (Get-Content $dockerfile) -replace 'FROM eclipse-temurin:8-jdk-alpine', 'FROM amazoncorretto:8-alpine-jdk' | Set-Content $dockerfile
            Write-Host "✅ 已更新 $dockerfile" -ForegroundColor Green
        }
    }
    
    Write-Host "现在可以使用 docker-compose up -d --build" -ForegroundColor Green
    exit 0
} catch {
    Write-Host "❌ Amazon Corretto镜像拉取失败" -ForegroundColor Red
}

# 方案3: 尝试使用阿里云镜像
Write-Host "`n方案3: 尝试拉取阿里云镜像..." -ForegroundColor Blue
try {
    docker pull registry.cn-hangzhou.aliyuncs.com/acs/openjdk:8-jdk-alpine
    Write-Host "✅ 阿里云镜像拉取成功" -ForegroundColor Green
    
    # 替换所有Dockerfile中的基础镜像
    Write-Host "正在更新Dockerfile..." -ForegroundColor Blue
    
    foreach ($dockerfile in $dockerfiles) {
        if (Test-Path $dockerfile) {
            (Get-Content $dockerfile) -replace 'FROM eclipse-temurin:8-jdk-alpine', 'FROM registry.cn-hangzhou.aliyuncs.com/acs/openjdk:8-jdk-alpine' | Set-Content $dockerfile
            Write-Host "✅ 已更新 $dockerfile" -ForegroundColor Green
        }
    }
    
    Write-Host "现在可以使用 docker-compose up -d --build" -ForegroundColor Green
    exit 0
} catch {
    Write-Host "❌ 阿里云镜像拉取失败" -ForegroundColor Red
}

# 方案4: 配置Docker镜像加速器
Write-Host "`n方案4: 配置Docker镜像加速器..." -ForegroundColor Blue
Write-Host "请手动配置Docker镜像加速器:" -ForegroundColor Yellow
Write-Host "1. 右键点击Docker Desktop图标" -ForegroundColor White
Write-Host "2. 选择 Settings -> Docker Engine" -ForegroundColor White
Write-Host "3. 添加以下配置到registry-mirrors数组中:" -ForegroundColor White
Write-Host '   "https://docker.mirrors.ustc.edu.cn"' -ForegroundColor Cyan
Write-Host '   "https://hub-mirror.c.163.com"' -ForegroundColor Cyan
Write-Host '   "https://mirror.baidubce.com"' -ForegroundColor Cyan
Write-Host "4. 点击Apply & Restart" -ForegroundColor White
Write-Host "5. 重新运行此脚本" -ForegroundColor White

Write-Host "`n如果所有方案都失败，请检查网络连接或联系系统管理员" -ForegroundColor Red
