#!/bin/bash

# 博通工单管理系统 Docker 一键部署脚本
# Author: Augment Agent
# Date: $(date +%Y-%m-%d)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose是否安装
check_prerequisites() {
    log_info "检查系统环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "Docker 和 Docker Compose 已安装"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，正在创建默认配置..."
        cp .env.example .env
        log_info "请编辑 .env 文件配置您的环境变量，特别是 Nacos 连接信息"
        log_info "配置完成后重新运行此脚本"
        exit 0
    fi
    log_success "环境变量文件检查完成"
}

# 编译项目
build_project() {
    log_info "开始编译项目..."
    
    if [ ! -f "pom.xml" ]; then
        log_error "未找到 pom.xml 文件，请确保在项目根目录执行此脚本"
        exit 1
    fi
    
    # 检查Maven是否安装
    if ! command -v mvn &> /dev/null; then
        log_error "Maven 未安装，请先安装 Maven"
        exit 1
    fi
    
    # 清理并编译项目
    log_info "执行 Maven 编译..."
    mvn clean install -DskipTests -q
    
    if [ $? -eq 0 ]; then
        log_success "项目编译完成"
    else
        log_error "项目编译失败"
        exit 1
    fi
}

# 停止现有服务
stop_services() {
    log_info "停止现有服务..."
    docker-compose down --remove-orphans
    log_success "现有服务已停止"
}

# 构建和启动服务
start_services() {
    log_info "构建并启动服务..."
    
    # 构建镜像
    log_info "构建 Docker 镜像..."
    docker-compose build --no-cache
    
    if [ $? -ne 0 ]; then
        log_error "Docker 镜像构建失败"
        exit 1
    fi
    
    # 启动服务
    log_info "启动服务..."
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        log_success "服务启动完成"
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    sleep 10
    
    # 显示服务状态
    docker-compose ps
    
    # 检查服务健康状态
    log_info "等待服务启动完成..."
    
    services=("quartz" "message" "monitor" "system" "workorder" "gateway")
    ports=(58082 58083 58084 58081 58085 58080)
    
    for i in "${!services[@]}"; do
        service=${services[$i]}
        port=${ports[$i]}
        
        log_info "检查 $service 服务 (端口: $port)..."
        
        # 等待服务启动
        for attempt in {1..30}; do
            if curl -f -s http://localhost:$port/actuator/health > /dev/null 2>&1; then
                log_success "$service 服务启动成功"
                break
            fi
            
            if [ $attempt -eq 30 ]; then
                log_warning "$service 服务可能启动失败，请检查日志"
                docker-compose logs $service
            else
                sleep 10
            fi
        done
    done
}

# 显示访问信息
show_access_info() {
    log_success "博通工单管理系统部署完成！"
    echo ""
    echo "==================================="
    echo "服务访问信息："
    echo "==================================="
    echo "🌐 网关服务:     http://localhost:58080"
    echo "📊 API文档:      http://localhost:58080/doc.html"
    echo "🔧 系统管理:     http://localhost:58081"
    echo "📋 工单管理:     http://localhost:58085"
    echo "⏰ 定时任务:     http://localhost:58082"
    echo "💬 消息服务:     http://localhost:58083"
    echo "📈 监控面板:     http://localhost:58084/admin"
    echo "==================================="
    echo ""
    echo "📝 查看日志: docker-compose logs -f [service-name]"
    echo "🔄 重启服务: docker-compose restart [service-name]"
    echo "🛑 停止服务: docker-compose down"
    echo ""
}

# 主函数
main() {
    echo "=========================================="
    echo "  博通工单管理系统 Docker 一键部署脚本"
    echo "=========================================="
    echo ""
    
    check_prerequisites
    check_env_file
    build_project
    stop_services
    start_services
    check_services
    show_access_info
}

# 执行主函数
main "$@"
