FROM eclipse-temurin:8-jdk-alpine

# 设置时区
RUN apk add --no-cache tzdata curl && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 创建应用目录
WORKDIR /app

# 复制jar文件
ARG JAR_FILE=target/bto-cloud-workorder.jar
COPY ${JAR_FILE} app.jar

# 设置JVM参数环境变量
ENV JAVA_OPTS="-Xmx2048m -Xms512m -Dspring.profiles.active=dev"

# 暴露端口
EXPOSE 58085

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=90s --retries=3 \
    CMD curl -f http://localhost:58085/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]