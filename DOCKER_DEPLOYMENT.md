# 博通工单管理系统 Docker 一键部署指南

## 📋 概述

本指南提供了博通工单管理系统的完整Docker容器化部署方案，实现6个微服务的一键启动和管理。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   外部 Nacos    │    │   外部 Redis    │    │   外部 MySQL    │
│   (8848)        │    │   (6379)        │    │   (3306)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
    ┌────────────────────────────┼────────────────────────────┐
    │                    Docker Network                       │
    │                                                         │
    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
    │  │   Quartz    │  │   Message   │  │   Monitor   │     │
    │  │   (58082)   │  │   (58083)   │  │   (58084)   │     │
    │  └─────────────┘  └─────────────┘  └─────────────┘     │
    │           │               │               │             │
    │  ┌─────────────┐  ┌─────────────┐                      │
    │  │   System    │  │  WorkOrder  │                      │
    │  │   (58081)   │  │   (58085)   │                      │
    │  └─────────────┘  └─────────────┘                      │
    │           │               │                             │
    │  ┌─────────────────────────────┐                       │
    │  │        Gateway              │                       │
    │  │        (58080)              │                       │
    │  └─────────────────────────────┘                       │
    └─────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装：
- Docker (版本 20.10+)
- Docker Compose (版本 1.29+)
- <PERSON><PERSON> (用于编译项目)
- 外部服务：Nacos、Redis、MySQL

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
# Windows: notepad .env
# Linux/Mac: vim .env
```

**重要配置项：**
```env
# Nacos配置（必须修改为实际地址）
NACOS_HOST=127.0.0.1
NACOS_PORT=8848
NACOS_NAMESPACE=

# 应用环境
SPRING_PROFILES_ACTIVE=dev
```

### 3. 一键部署

#### Windows环境：
```powershell
# 1. 编译项目
mvn clean install -DskipTests

# 2. 构建并启动服务
docker-compose up -d --build

# 3. 查看服务状态
docker-compose ps
```

#### Linux/Mac环境：
```bash
# 使用部署脚本（推荐）
./deploy.sh

# 或手动执行
mvn clean install -DskipTests
docker-compose up -d --build
```

## 📊 服务配置详情

### 服务启动顺序和依赖关系

1. **第一层（基础服务）**：
   - `quartz` (定时任务) - JVM: 1024m/512m
   - `message` (消息服务) - JVM: 1024m/512m  
   - `monitor` (监控服务) - JVM: 512m/256m

2. **第二层（核心服务）**：
   - `system` (系统服务) - JVM: 2048m/512m
   - `workorder` (工单服务) - JVM: 2048m/512m

3. **第三层（网关服务）**：
   - `gateway` (网关服务) - JVM: 1024m/256m

### 端口映射

| 服务 | 容器端口 | 主机端口 | 描述 |
|------|----------|----------|------|
| gateway | 58080 | 58080 | 网关服务，系统入口 |
| system | 58081 | 58081 | 系统管理服务 |
| quartz | 58082 | 58082 | 定时任务服务 |
| message | 58083 | 58083 | 消息通知服务 |
| monitor | 58084 | 58084 | 监控管理服务 |
| workorder | 58085 | 58085 | 工单管理服务 |

## 🛠️ 服务管理

### 基本命令

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启所有服务
docker-compose restart

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service-name]

# 重新构建服务
docker-compose build --no-cache [service-name]
```

### 使用管理脚本（Linux/Mac）

```bash
# 启动服务
./manage.sh start [service-name]

# 停止服务
./manage.sh stop [service-name]

# 重启服务
./manage.sh restart [service-name]

# 查看日志
./manage.sh logs [service-name]

# 检查健康状态
./manage.sh health

# 清理资源
./manage.sh clean
```

## 🔍 服务验证

### 健康检查

所有服务都配置了健康检查端点：

```bash
# 检查网关服务
curl http://localhost:58080/actuator/health

# 检查系统服务
curl http://localhost:58081/actuator/health

# 检查工单服务
curl http://localhost:58085/actuator/health
```

### 访问地址

- **系统入口**: http://localhost:58080
- **API文档**: http://localhost:58080/doc.html
- **监控面板**: http://localhost:58084/admin
- **系统管理**: http://localhost:58081
- **工单管理**: http://localhost:58085

## 🐛 故障排除

### 常见问题

1. **Docker镜像拉取失败** ⭐ **最常见问题**
   ```bash
   # 错误示例：
   # failed to resolve source metadata for docker.io/library/openjdk:8-jdk-alpine
   # unexpected status from HEAD request: 403 Forbidden
   ```

   **解决方案：**

   **Windows环境：**
   ```powershell
   # 运行镜像修复脚本
   .\fix-docker-images.ps1
   ```

   **Linux/Mac环境：**
   ```bash
   # 运行镜像修复脚本
   chmod +x fix-docker-images.sh
   ./fix-docker-images.sh
   ```

   **手动解决方案：**
   - 配置Docker镜像加速器
   - 或手动替换Dockerfile中的基础镜像为可用镜像：
     ```dockerfile
     # 替换为以下任一可用镜像：
     FROM amazoncorretto:8-alpine-jdk
     FROM registry.cn-hangzhou.aliyuncs.com/acs/openjdk:8-jdk-alpine
     FROM ccr.ccs.tencentyun.com/library/openjdk:8-jdk-alpine
     ```

2. **服务启动失败**
   ```bash
   # 查看具体服务日志
   docker-compose logs [service-name]

   # 检查容器状态
   docker-compose ps
   ```

3. **Nacos连接失败**
   - 检查`.env`文件中的Nacos配置
   - 确保Nacos服务正常运行
   - 验证网络连通性

4. **内存不足**
   - 调整`.env`文件中的JVM参数
   - 或修改docker-compose.yml中的environment配置

5. **端口冲突**
   - 检查端口是否被占用：`netstat -tulpn | grep :58080`
   - 修改docker-compose.yml中的端口映射

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs -f gateway

# 查看最近100行日志
docker-compose logs --tail=100 system
```

## 📈 性能优化

### JVM调优建议

根据服务器配置调整JVM参数：

```yaml
# 高配置服务器
GATEWAY_JAVA_OPTS=-Xmx2048m -Xms512m -XX:+UseG1GC
SYSTEM_JAVA_OPTS=-Xmx4096m -Xms1024m -XX:+UseG1GC
WORKORDER_JAVA_OPTS=-Xmx4096m -Xms1024m -XX:+UseG1GC

# 低配置服务器
GATEWAY_JAVA_OPTS=-Xmx512m -Xms256m
SYSTEM_JAVA_OPTS=-Xmx1024m -Xms512m
WORKORDER_JAVA_OPTS=-Xmx1024m -Xms512m
```

### 资源监控

```bash
# 查看容器资源使用情况
docker stats

# 查看特定容器资源使用
docker stats bto-cloud-gateway
```

## 🔒 安全建议

1. **生产环境配置**：
   - 修改默认端口
   - 配置防火墙规则
   - 使用HTTPS

2. **敏感信息管理**：
   - 不要将`.env`文件提交到版本控制
   - 使用Docker secrets管理敏感配置

3. **网络安全**：
   - 限制容器间网络访问
   - 配置适当的网络策略

## 📝 更新日志

- **v1.0.0**: 初始版本，支持6个微服务的容器化部署
- 优化了服务启动顺序和依赖关系
- 添加了健康检查和自动重启机制
- 提供了完整的管理脚本和文档
